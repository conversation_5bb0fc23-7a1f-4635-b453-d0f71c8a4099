import React, {createContext, useContext, useEffect, useState} from "react";
import {shallowEqual, useDispatch, useSelector} from "react-redux";
import {fillAssociatedEntityLevel, fillEntityLevel} from "../../../state/slices/CoreEntity";
import apiClient from "../../../auth/apiClient";
import {useEnqueueSnackbar} from "../../../hooks/useEnqueueSnackbar";
import * as c from "../../../constants";

const EntitiesSelectedContext = createContext({});

export const useEntitySelectedContext = () => useContext(EntitiesSelectedContext);


const getAssociatedEntities = (entityID, entityList) => {
    const associatedEntityIds = new Set();

    const directlyAssociated = entityList.filter(entity =>
        entity.parententityid === entityID ||
        entity.entityid === entityID
    );

    directlyAssociated.forEach(entity => {
        associatedEntityIds.add(entity.entityid);
    });

    let foundNew = true;
    while (foundNew) {
        foundNew = false;

        entityList.forEach(entity => {
            if (!associatedEntityIds.has(entity.entityid) &&
                entity.parententityid &&
                associatedEntityIds.has(entity.parententityid)) {

                // Found a new descendant
                associatedEntityIds.add(entity.entityid);
                foundNew = true;
            }
        });
    }
    return entityList.filter(entity => associatedEntityIds.has(entity.entityid));
}

const flattenEntityTree = (entity) => {
    if (!entity) return [];

    // Start with the current entity
    let entities = [entity];

    // If the entity has children, recursively flatten and add them
    if (entity.haschildren && entity.children && Array.isArray(entity.children)) {
        entity.children.forEach(child => {
            entities = entities.concat(flattenEntityTree(child));
        });
    }
    return entities;
};

const groupEntitiesByParent = (entities) => {
    if (!entities || entities.length === 0) return {};
    const groupedEntities = {};

    const entityIdToName = {};
    let entityParentId = {};
    const entityParentTypeId = {};

    entities.forEach(entity => {
        entityIdToName[entity.entityid] = entity.entityname || entity.name || entity.entityid;
        entityParentId[entity.entityid] = {
            parententityid: entity.parententityid
        };
        entityParentTypeId[entity.entityid] = {
            typeid: entity.typeid
        };
    });

    entities.forEach(entity => {
        const parentId = entity.parententityid;

        if (!parentId) return;
        const parentName = entityIdToName[parentId] || parentId;
        if (!groupedEntities[parentName]) {
            groupedEntities[parentName] = {
                entityID: parentId,
                children: [],
                parentID: entityParentId[parentId]?.parententityid,
                typeID: entityParentTypeId[parentId]?.typeid || c.ENTITY_TYPE[entityParentTypeId[parentId]?.entitytype]
            };
        }

        groupedEntities[parentName].children.push({...entity});
    });

    return groupedEntities;
};

export const EntitySelectedProvider = ({children}) => {
    const [entitySelected, setEntitySelected] = useState("");
    const [associatedEntities, setAssociatedEntities] = useState({});
    const [isLoading, setIsLoading] = useState(true);
    const contextID = useSelector(state => state.coreEntities.ContextID, shallowEqual);
    const userID = useSelector(state => state.user.UserID, shallowEqual);
    const OrgEntities = useSelector((state) => state.orgTree.entities, shallowEqual);
    const rootEntity = useSelector((state) => {
        const entities = state.coreEntities?.entities || {};
        return Object.values(entities).find(entity => !entity?.parententityid)?.entityid;
    }, shallowEqual);
    const dispatch = useDispatch();
    const enqueueSnackbar = useEnqueueSnackbar();

    const [entityDetailsState, setEntityDetailsState] = useState({
        entityID: "",
        isOpen: false
    });

    useEffect(() => {
        async function loadAndSetEntities() {
            setIsLoading(true);

            try {
                const entities = await getEntities(contextID);
                console.log("entities", entities)
                if (entities) {
                    const groupedEntities = groupEntitiesByParent(entities);

                    console.log("groupedEntities", groupedEntities)
                    setAssociatedEntities(groupedEntities);
                    await dispatch(fillAssociatedEntityLevel({
                        entityID: contextID,
                        associatedEntities: entities
                    }))
                    await dispatch(fillEntityLevel({
                        entityID: contextID,
                        userID
                    }))
                }

                setEntitySelected(contextID);
                setIsLoading(false);
            } catch (error) {
                enqueueSnackbar("Error loading entities", {
                    variant: "error",
                    tag: "FailedToLoadEntities",
                });

                console.log("error", error);
                setIsLoading(false);
            }
        }

        loadAndSetEntities();
    }, [contextID, ]);

    // update the associated entities when the OrgEntities change (new entity added or deleted)
    useEffect(() => {
        if (entitySelected) {
            const updatedAssociatedEntities = getAssociatedEntities(
                entitySelected,
                Object.values(OrgEntities)
            );

            const groupedEntities = groupEntitiesByParent(updatedAssociatedEntities);
            setAssociatedEntities(groupedEntities);
        }
    }, [OrgEntities, entitySelected]);

    const handleEntityDetailSelection = (entityID, openSetting = true) => {
        setEntitySelected(entityID)
        setEntityDetailsState({entityID: entityID, isOpen: openSetting});
    };


    const getEntities = async (entityID) => {
        try {
            const response = await apiClient.get(`entities/${entityID}?limit=4`);
            if (!response || response.data.length === 0) return null;

            const entityTree = response.data;
            return entityTree.flatMap(entity => flattenEntityTree(entity));

        } catch (error) {
            enqueueSnackbar("error with endpoint", {
                variant: "error",
                tag: "FailedToGetEntities",
            });
            return null;
        }
    }
    // when an entity is selected it will call the tree endpoint then populate the associated entities and selected Entity
    // if the selected entity is the same as the current selected entity then do not call the endpoint
    const onSelectedEntity = async (entity) => {
        if (entity.entityid === entitySelected) return;

        if (entity.entityid === contextID && contextID !== entitySelected) {
            setEntitySelected(contextID); //context entity is already selected
            const contextAssociatedEntities = getAssociatedEntities(entity.entityid, Object.values(OrgEntities))
            const groupedEntities = groupEntitiesByParent(contextAssociatedEntities);
            setAssociatedEntities(groupedEntities);
            setIsLoading(false);
            return;
        }
        setIsLoading(true);
        try {
            const foundEntity = OrgEntities[entity.entityid];
            // if the data exist in the org and has been fetched before then use the data from the store
            if (foundEntity && foundEntity.details && foundEntity.entityname) {
                const orgAssociatedEntities = getAssociatedEntities(entity.entityid, Object.values(OrgEntities))

                const groupedEntities = groupEntitiesByParent(orgAssociatedEntities);
                setEntitySelected(entity.entityid);
                setAssociatedEntities(groupedEntities);
                setIsLoading(false);
                return;
            }

            // let newAssociatedEntities = await getEntities(entity.entityid);
            // if (!newAssociatedEntities || newAssociatedEntities.length === 0) {
            //     setIsLoading(false);
            //     return;
            // }

            // TODO: ill need to revisit at some point and make this more efficient
            // technically im only doing this to populate the children with there settings
            // will probably need to make a new api call to get the settings for an entity OR update the existing api get that
            await dispatch(fillEntityLevel({
                entityID: entity.entityid,
                userID
            }))
            // await dispatch(fillAssociatedEntityLevel({
            //     entityID: entity.entityid,
            //     associatedEntities: newAssociatedEntities
            // }))
            //
            // const hasChildren = entity?.children?.length > 0;
            // if (!hasChildren) {
            //     setIsLoading(false);
            //     setEntitySelected(entity.entityid);
            //     return;
            // }
            // const groupedEntities = groupEntitiesByParent(newAssociatedEntities);
            // setEntitySelected(entity.entityid);
            //
            // setAssociatedEntities(groupedEntities);
            setIsLoading(false);

        } catch (error) {
            enqueueSnackbar("Failed to select entity", {
                variant: "error",
                tag: "FailedToSelectEntity",
            });
            setIsLoading(false);

        }
    }

    return (
        <EntitiesSelectedContext.Provider value={{
            associatedEntities,
            entitySelected,
            onSelectedEntity,
            isLoading,
            entityDetailsState,
            handleEntityDetailSelection
        }}>
            {children}
        </EntitiesSelectedContext.Provider>
    );
};

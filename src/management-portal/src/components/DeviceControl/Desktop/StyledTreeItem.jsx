import React, {useEffect, useState} from "react";
import TreeItem from "@material-ui/lab/TreeItem";
import * as c from "../../../constants";
import {useStyles} from "./StyledTreeItem.style";
import {useEntitySelectedContext, useSeverityState} from "../Hooks";
import Collapse from '@material-ui/core/Collapse';
import {withStyles} from '@material-ui/core/styles';
import {EntityTypeIcons, PlusSquare} from "./Icons";
import {Box, Typography} from "@material-ui/core";
import {shallowEqual, useSelector} from "react-redux";
import { Tooltip } from '@material-ui/core';
import {selectEntityForOrgTree, selectOrgTreeEntityById} from "../../../state/slices/OrgTree";

const styles = theme => ({
    root: {
        transition: 'opacity 300ms cubic-bezier(0.4, 0, 0.2, 1), transform 300ms cubic-bezier(0.4, 0, 0.2, 1)',
        opacity: 0,
        transform: 'translate3d(20px, 0, 0)',
    },
    entered: {
        opacity: 1,
        transform: 'translate3d(0, 0, 0)',
    },
    exiting: {
        opacity: 0,
        transform: 'translate3d(20px, 0, 0)',
    }
});

export const TransitionComponent = withStyles(styles)(({classes, ...props}) => {
    const [state, setState] = useState(props.in ? 'entered' : 'exited');

    useEffect(() => {
        if (props.in) {
            setState('entering');
            const timer = setTimeout(() => setState('entered'), 10);
            return () => clearTimeout(timer);
        } else {
            setState('exiting');
        }
    }, [props.in]);

    return (
        <div
            className={`${classes.root} ${state === 'entered' ? classes.entered : state === 'exiting' ? classes.exiting : ''}`}>
            <Collapse {...props} />
        </div>
    );
});

const StyledTreeItemPure = withStyles((theme) => ({
    iconContainer: {
        '& .close': {
            opacity: 0.3,
        },
        color: theme.palette.primary.main
    },
    group: {
        marginLeft: 7,
        paddingLeft: 18,
        borderLeft: `1px solid ${theme.palette.primary.main}60`,
    },
}))((props) => <TreeItem {...props} TransitionComponent={TransitionComponent}/>);


export const StyledTreeItem = ({entityID, severityInfo, children}) => {
    const classes = useStyles();
    const entity = useSelector((state) => selectOrgTreeEntityById(state,entityID), shallowEqual);
    console.log("entity", entity);
    const severityClass = useSeverityState(severityInfo);
    const {onSelectedEntity, handleEntityDetailSelection} = useEntitySelectedContext()
    const isAdmin = useSelector((state) => state.user.isAdmin, shallowEqual);
    const isContext = useSelector((state) => state.coreEntities.ContextID === entity?.entityid, shallowEqual)
    const onTreeItemClicked = async (event) => {

        const hasChildren = entity?.children?.length > 0;

        event.preventDefault()
        if (!entity?.details && entity?.typeid !== c.ENTITY_TYPE.Tenant && entity?.typeid !== c.ENTITY_TYPE.Device)
            await onSelectedEntity(entity);

        if (!hasChildren || (entity?.typeid === c.ENTITY_TYPE.Tenant && isAdmin)) {

            if (entity?.typeid === c.ENTITY_TYPE.Device)
                await onSelectedEntity({entityid: entity?.parententityid});

            handleEntityDetailSelection(entity?.entityid, true);
            return
        }
        if((entity?.typeid === c.ENTITY_TYPE.Tenant && !isAdmin))
           return

        handleEntityDetailSelection(entity?.entityid, false);
    };


    const labelContent = (
        <Box display="flex" alignItems="center">
            <Box mr={0.4} display="flex" alignItems="center" flexShrink={0}
                 className={severityClass ? classes[severityClass] : classes.entityTypeContainer}>
                <EntityTypeIcons
                    entityType={entity?.typeid}
                    style={{width: 20, height: 20}}
                    fontSize="inherit"
                    className={classes.entityTypeIcon}
                />
            </Box>
            <Typography
                className={isContext ? classes.contextLabel : null}
                noWrap
                style={{
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    width: "80%"
                }}
                component={props => (
                    <Tooltip
                        title={entity?.name}
                        arrow
                        disableTouchListener={true}
                        PopperProps={{
                            disablePortal: true,
                            modifiers: {
                                preventOverflow: {
                                    enabled: true,
                                    boundariesElement: 'viewport'
                                }
                            }
                        }}
                    >
                        <span {...props}>{entity?.name}</span>
                    </Tooltip>
                )}
            />
        </Box>
    );


    return (
        <>
            <StyledTreeItemPure
                key={entity?.entityid}
                nodeId={entity?.entityid}
                data-id={entity?.name}
                className={`${isContext ? classes.treeLineContextItem : classes.treeLineItem}`}
                label={labelContent}
                onClick={(event) => {
                    // This prevents the click from expanding/collapsing the node
                    event.preventDefault();
                    event.stopPropagation();
                    return false;
                }}
                onLabelClick={onTreeItemClicked}
                endIcon={entity?.typeid === c.ENTITY_TYPE.Device ? null : <PlusSquare className="close"/>}
            >
                {children}
            </StyledTreeItemPure>
        </>
    );
}
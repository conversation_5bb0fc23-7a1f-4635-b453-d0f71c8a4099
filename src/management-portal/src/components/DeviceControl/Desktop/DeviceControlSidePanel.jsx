import React, {useEffect, useMemo, useState} from "react";
import {Divider, Paper} from "@material-ui/core";
import {useStyles} from "./DeviceControlSidePanel.style";
import {fuzzyMatch, useEntitiesWithSeverity, useEntitySelectedContext, useSidePanelExpansion} from "../Hooks";
import TreeView from "@material-ui/lab/TreeView";
import {StyledTreeItem} from "./StyledTreeItem";
import {shallowEqual, useSelector} from "react-redux";
import {selectOrgTreeData} from "../../../state/slices/OrgTree";
import {MinusSquare, PlusSquare} from "./Icons";
import PropTypes from "prop-types";
import {FilterInput} from "../Shared";
import Container from "@material-ui/core/Container";
import _ from 'lodash';
import EntityService from "../../../services/EntityService";
import apiClient from "../../../auth/apiClient";
import {useFeatureFlag} from "../../../hooks/useFeatureFlags";
import * as c from "../../../constants";


const entityService = new EntityService(apiClient);

export const DeviceControlSidePanel = () => {
    const classes = useStyles();
    const {entitySelected, isLoading} = useEntitySelectedContext();
    const contractNestFeature = useFeatureFlag("Contract Nest");
    const [facilitiesByScope, setFacilitiesByScope] = useState([]);
    const contextID = useSelector(state => state.coreEntities.ContextID, shallowEqual);

    const memoizedSelector = useMemo(
        () => (state) => selectOrgTreeData(state, facilitiesByScope),
        [facilitiesByScope]
    );

    const treeData = useSelector(memoizedSelector, shallowEqual);

    console.log("treeData", treeData);

    // TODO: JonathanPayares needs to be refactored
    useEffect(() => {
        console.log("test:side panel");
        async function getFacilitiesByScope() {
            const result = await entityService.getPropertiesByScopes();

            const hasContextIdInTree = (node) => {
                if (node.id === contextID) return true;
                if (node.children && node.children.length > 0) {
                    // Check all children recursively
                    console.log("node", node);
                    for (const child of node.children) {
                        if (hasContextIdInTree(child)) {
                            return true;
                        }
                    }
                }
                return false;
            };

            const topLevelIds = result.data
                .filter(item => hasContextIdInTree(item))
                .map(item => item.id);

            topLevelIds.push(contextID);
            setFacilitiesByScope(topLevelIds);
        }

        getFacilitiesByScope();
    }, [contextID]);

    const [filterText, setFilterText] = useState('');

    const {entitiesWithSeverity} = useEntitiesWithSeverity();
    const {entitiesToExpand, defaultSelected} = useSidePanelExpansion();
    const handleFilterChange = (value) => {
        setFilterText(value);
    };

    const renderTree = nodes => {

        if (nodes?.typeid === c.ENTITY_TYPE.Area && nodes.parententityid === contextID && !contractNestFeature) return null;

        console.log("test", nodes);
        return (
            <StyledTreeItem
                entityID={nodes?.entityid}
                key={nodes?.entityid}
                severityInfo={entitiesWithSeverity[nodes?.entityid]?.SeverityInfo}
            >
                {Array.isArray(nodes?.children) ? nodes?.children.map((node) => renderTree(node)) : null}
            </StyledTreeItem>
        )
    };

    const doesNodeMatch = (node) => {
        if (!node || !filterText.trim()) return false;

        return (
            fuzzyMatch(node.name || '', filterText) ||
            fuzzyMatch(node.entityid || '', filterText) ||
            fuzzyMatch(node.details?.description || '', filterText)
        );
    };

    const filterTreeRecursive = (node) => {
        if (!node) return null;

        // Check if the current node matches
        const nodeMatches = doesNodeMatch(node);

        // Recursively filter children
        let filteredChildren = [];
        if (Array.isArray(node.children) && node.children.length > 0) {
            // Process all children recursively
            filteredChildren = node.children
                .map(child => filterTreeRecursive(child))
                .filter(Boolean); // Remove null results
        }

        // Keep this node if:
        // 1. There's no filter text (show everything)
        // 2. This node matches the filter
        // 3. Any of its children match the filter
        if (!filterText.trim() || nodeMatches || filteredChildren.length > 0) {
            return {
                ...node,
                children: filteredChildren
            };
        }

        return null;
    };

    const filteredTreeData = useMemo(() => {
        return filterTreeRecursive(treeData);
    }, [treeData, filterText]);

    return (
        <Paper elevation={3} className={classes.paperContainer}>
            <div className={classes.filterInputContainer}>
                <FilterInput onChange={handleFilterChange}/>
            </div>
            <Divider/>
            <Container className={classes.treeViewContainer}>
                {treeData && (
                    <TreeView
                        defaultExpanded={entitiesToExpand}
                        defaultSelected={defaultSelected}
                        selected={entitySelected}
                        defaultCollapseIcon={<MinusSquare/>}
                        defaultExpandIcon={<PlusSquare/>}
                    >
                        {renderTree(filteredTreeData)}
                    </TreeView>
                )}
            </Container>
        </Paper>
    );
}

DeviceControlSidePanel.propTypes = {
    onSelected: PropTypes.string
};

import React from "react";
import { shallow } from "enzyme";
import ThemeProvider, { ThemeSwitchContext } from "./ThemeProvider";

describe("ThemeProvider", () => {
  it("should be in light mode by default", () => {
    const wrapper = shallow(<ThemeProvider />);
    expect(wrapper.props().theme.palette.type === "light").toBe(true);
  });

  it("should change to dark mode when toggle theme called", () => {
    const wrapper = shallow(<ThemeProvider />);
    const contextProvider = wrapper.find(ThemeSwitchContext.Provider);
    expect(wrapper.props().theme.palette.type).toBe("light");
    contextProvider.props().value.toggleTheme();
    expect(wrapper.props().theme.palette.type).toBe("dark");
  });
});
